import { cookies } from "next/headers"
import { getAdminAuth } from "@/lib/firebase-admin"
import { User } from "@/lib/domains/user/user.types"
import { UserServerService } from "../domains/user/user.service"

/**
 * Server-side authentication service for SSR
 */
export class AuthServerService {
  /**
   * Get authenticated user from server-side context
   * Checks both cookies and Authorization header
   */
  static async getServerUser(): Promise<User | null> {
    try {
      // Try to get token from cookies first (preferred for SSR)
      const cookieStore = await cookies()
      let idToken = cookieStore.get("firebase-auth-token")?.value

      // Fallback to Authorization header if no cookie
      if (!idToken) {
        // This would be set by middleware or client-side auth
        const authHeader = cookieStore.get("auth-token")?.value
        if (authHeader?.startsWith("Bearer ")) {
          idToken = authHeader.substring(7)
        }
      }

      if (!idToken) {
        return null
      }

      // Get Firebase Admin Auth instance
      const adminAuth = await getAdminAuth()

      // Verify the session cookie with Firebase Admin
      const decodedToken = await adminAuth.verifySessionCookie(idToken, true)

      if (!decodedToken.uid) {
        return null
      }

      // Use UserServerService to get user data with proper serialization
      const user = await UserServerService.getUser(decodedToken.uid)
      return user
    } catch (error) {
      console.error("Error getting server user:", error)
      return null
    }
  }

  /**
   * Verify if user is authenticated (lightweight check)
   */
  static async isAuthenticated(): Promise<boolean> {
    try {
      const user = await this.getServerUser()
      return !!user
    } catch {
      return false
    }
  }

  /**
   * Get user ID from token without full user data fetch
   */
  static async getUserId(): Promise<string | null> {
    try {
      const cookieStore = await cookies()
      let idToken = cookieStore.get("firebase-auth-token")?.value

      if (!idToken) {
        const authHeader = cookieStore.get("auth-token")?.value
        if (authHeader?.startsWith("Bearer ")) {
          idToken = authHeader.substring(7)
        }
      }

      if (!idToken) {
        return null
      }

      const adminAuth = await getAdminAuth()
      const decodedToken = await adminAuth.verifySessionCookie(idToken, true)
      return decodedToken.uid || null
    } catch (error) {
      console.error("Error getting user ID:", error)
      return null
    }
  }
}
